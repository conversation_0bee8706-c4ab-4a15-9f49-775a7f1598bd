#!/usr/bin/env python3
"""
Solana Token Listener for Official <PERSON> (ERIC) Token
Monitors real-time token activity using Solana WebSocket RPC
"""

import asyncio
import json
import websockets
import logging
from datetime import datetime
from typing import Dict, Any, Optional
import base64

# Configure logging with UTF-8 encoding for Windows compatibility
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('token_activity.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SolanaTokenListener:
    def __init__(self, token_mint: str, rpc_url: str = "wss://mainnet.helius-rpc.com/?api-key=e3996982-5073-4b8b-942d-1d774b777012"):
        """
        Initialize the Solana token listener

        Args:
            token_mint: The mint address of the token to monitor
            rpc_url: Solana WebSocket RPC URL
        """
        self.token_mint = token_mint
        self.rpc_url = rpc_url
        self.websocket = None
        self.subscription_id = None

    async def connect(self):
        """Establish WebSocket connection to Solana RPC"""
        try:
            self.websocket = await websockets.connect(self.rpc_url)
            logger.info(f"Connected to Solana WebSocket: {self.rpc_url}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to WebSocket: {e}")
            return False

    async def subscribe_to_token_accounts(self):
        """Subscribe to all token accounts for the specified mint"""
        if not self.websocket:
            logger.error("WebSocket not connected")
            return False

        # Subscribe to program account changes for SPL Token program
        # This will catch all token account changes for our mint
        subscription_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "programSubscribe",
            "params": [
                "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",  # SPL Token Program ID
                {
                    "encoding": "jsonParsed",
                    "filters": [
                        {
                            "memcmp": {
                                "offset": 0,
                                "bytes": self.token_mint
                            }
                        }
                    ]
                }
            ]
        }

        try:
            await self.websocket.send(json.dumps(subscription_request))
            response = await self.websocket.recv()
            response_data = json.loads(response)

            if "result" in response_data:
                self.subscription_id = response_data["result"]
                logger.info(f"Successfully subscribed to token accounts. Subscription ID: {self.subscription_id}")
                return True
            else:
                logger.error(f"Subscription failed: {response_data}")
                return False

        except Exception as e:
            logger.error(f"Error subscribing to token accounts: {e}")
            return False

    async def subscribe_to_logs(self):
        """Subscribe to transaction logs mentioning the token"""
        if not self.websocket:
            logger.error("WebSocket not connected")
            return False

        logs_subscription = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "logsSubscribe",
            "params": [
                {
                    "mentions": [self.token_mint]
                },
                {
                    "commitment": "confirmed"
                }
            ]
        }

        try:
            await self.websocket.send(json.dumps(logs_subscription))
            response = await self.websocket.recv()
            response_data = json.loads(response)

            if "result" in response_data:
                logger.info(f"Successfully subscribed to transaction logs. Subscription ID: {response_data['result']}")
                return True
            else:
                logger.error(f"Logs subscription failed: {response_data}")
                return False

        except Exception as e:
            logger.error(f"Error subscribing to logs: {e}")
            return False

    def parse_token_account_data(self, account_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse token account data from the notification"""
        try:
            if account_data.get("encoding") == "jsonParsed":
                parsed_info = account_data.get("data", {}).get("parsed", {}).get("info", {})
                return {
                    "mint": parsed_info.get("mint"),
                    "owner": parsed_info.get("owner"),
                    "token_amount": parsed_info.get("tokenAmount", {}),
                    "state": parsed_info.get("state")
                }
        except Exception as e:
            logger.error(f"Error parsing token account data: {e}")
        return None

    def format_token_amount(self, token_amount: Dict[str, Any]) -> str:
        """Format token amount for display"""
        amount = token_amount.get("uiAmount", 0)
        decimals = token_amount.get("decimals", 0)
        return f"{amount:,.{decimals}f}"

    async def handle_notification(self, message: Dict[str, Any]):
        """Handle incoming WebSocket notifications"""
        try:
            method = message.get("method")
            params = message.get("params", {})

            if method == "programNotification":
                result = params.get("result", {})
                account_info = result.get("value", {})
                account_data = account_info.get("account", {})

                # Parse token account data
                parsed_data = self.parse_token_account_data(account_data)
                if parsed_data and parsed_data["mint"] == self.token_mint:
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    token_amount = parsed_data.get("token_amount", {})
                    formatted_amount = self.format_token_amount(token_amount)

                    logger.info(f"[TOKEN UPDATE] {timestamp}")
                    logger.info(f"   Owner: {parsed_data['owner']}")
                    logger.info(f"   Balance: {formatted_amount} ERIC")
                    logger.info(f"   State: {parsed_data['state']}")
                    logger.info("-" * 60)

            elif method == "logsNotification":
                result = params.get("result", {})
                signature = result.get("value", {}).get("signature")
                logs = result.get("value", {}).get("logs", [])

                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                logger.info(f"[TRANSACTION LOG] {timestamp}")
                logger.info(f"   Signature: {signature}")
                for log in logs:
                    logger.info(f"   Log: {log}")
                logger.info("-" * 60)

        except Exception as e:
            logger.error(f"Error handling notification: {e}")

    async def listen(self):
        """Main listening loop"""
        logger.info(f"[STARTING] Listening for ERIC token activity...")
        logger.info(f"Token Mint: {self.token_mint}")
        logger.info("=" * 60)

        try:
            while True:
                if self.websocket:
                    message = await self.websocket.recv()
                    data = json.loads(message)

                    # Skip subscription confirmation messages
                    if "method" in data:
                        await self.handle_notification(data)
                else:
                    logger.error("WebSocket connection lost")
                    break

        except websockets.exceptions.ConnectionClosed:
            logger.error("WebSocket connection closed")
        except Exception as e:
            logger.error(f"Error in listening loop: {e}")

    async def start(self):
        """Start the token listener"""
        if await self.connect():
            # Subscribe to both token accounts and transaction logs
            account_sub = await self.subscribe_to_token_accounts()
            logs_sub = await self.subscribe_to_logs()

            if account_sub or logs_sub:
                await self.listen()
            else:
                logger.error("Failed to establish any subscriptions")
        else:
            logger.error("Failed to connect to Solana WebSocket")

async def main():
    # ERIC Token mint address (you'll need to get the complete address)
    # From the image, it appears to be 9BMok...MtRfr
    # You'll need to provide the complete mint address
    ERIC_TOKEN_MINT = "9BiMokLHE5oS4mBtYejP53JwPMhDFzwwTwT9DjMtRtfr"  # Replace with complete mint address

    print("🎯 Solana ERIC Token Listener")
    print("=" * 50)
    print(f"Monitoring token: {ERIC_TOKEN_MINT}")
    print("Press Ctrl+C to stop")
    print("=" * 50)

    listener = SolanaTokenListener(ERIC_TOKEN_MINT)

    try:
        await listener.start()
    except KeyboardInterrupt:
        print("\n👋 Stopping token listener...")
        logger.info("Token listener stopped by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
