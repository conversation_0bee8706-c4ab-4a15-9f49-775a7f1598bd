#!/usr/bin/env python3
"""
Script to get complete token information for ERIC token
"""

import asyncio
import json
import aiohttp
from typing import Dict, Any, Optional

class SolanaTokenInfo:
    def __init__(self, rpc_url: str = "https://api.mainnet-beta.solana.com"):
        self.rpc_url = rpc_url
    
    async def search_token_by_symbol(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Search for token by symbol using Jupiter API"""
        try:
            async with aiohttp.ClientSession() as session:
                # Jupiter API for token list
                url = "https://token.jup.ag/all"
                async with session.get(url) as response:
                    if response.status == 200:
                        tokens = await response.json()
                        
                        # Search for ERIC token
                        for token in tokens:
                            if token.get("symbol", "").upper() == symbol.upper():
                                return token
        except Exception as e:
            print(f"Error searching token: {e}")
        return None
    
    async def get_token_info(self, mint_address: str) -> Optional[Dict[str, Any]]:
        """Get detailed token information from Solana RPC"""
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getAccountInfo",
            "params": [
                mint_address,
                {
                    "encoding": "jsonParsed"
                }
            ]
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(self.rpc_url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("result")
        except Exception as e:
            print(f"Error getting token info: {e}")
        return None
    
    async def get_token_supply(self, mint_address: str) -> Optional[Dict[str, Any]]:
        """Get token supply information"""
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getTokenSupply",
            "params": [mint_address]
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(self.rpc_url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("result")
        except Exception as e:
            print(f"Error getting token supply: {e}")
        return None

async def main():
    print("🔍 Searching for ERIC Token Information...")
    print("=" * 50)
    
    token_info = SolanaTokenInfo()
    
    # Search for ERIC token
    eric_token = await token_info.search_token_by_symbol("ERIC")
    
    if eric_token:
        mint_address = eric_token.get("address")
        print(f"✅ Found ERIC Token!")
        print(f"Name: {eric_token.get('name', 'N/A')}")
        print(f"Symbol: {eric_token.get('symbol', 'N/A')}")
        print(f"Mint Address: {mint_address}")
        print(f"Decimals: {eric_token.get('decimals', 'N/A')}")
        print("-" * 50)
        
        # Get additional token information
        if mint_address:
            print("📊 Getting additional token information...")
            
            # Get token account info
            account_info = await token_info.get_token_info(mint_address)
            if account_info and account_info.get("value"):
                parsed_data = account_info["value"]["data"]["parsed"]["info"]
                print(f"Supply: {parsed_data.get('supply', 'N/A')}")
                print(f"Decimals: {parsed_data.get('decimals', 'N/A')}")
                print(f"Mint Authority: {parsed_data.get('mintAuthority', 'N/A')}")
                print(f"Freeze Authority: {parsed_data.get('freezeAuthority', 'N/A')}")
            
            # Get token supply
            supply_info = await token_info.get_token_supply(mint_address)
            if supply_info and supply_info.get("value"):
                supply_data = supply_info["value"]
                ui_amount = supply_data.get("uiAmount", 0)
                print(f"Current Supply: {ui_amount:,.2f} ERIC")
            
            print("-" * 50)
            print(f"🎯 Use this mint address in token_listener.py:")
            print(f"ERIC_TOKEN_MINT = \"{mint_address}\"")
    else:
        print("❌ ERIC token not found in Jupiter token list")
        print("You may need to manually find the mint address")
        print("Try checking solscan.io or other Solana explorers")

if __name__ == "__main__":
    asyncio.run(main())
