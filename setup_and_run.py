#!/usr/bin/env python3
"""
Setup and run script for ERIC token listener
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False

def run_token_info():
    """Run the token info script to get ERIC token details"""
    print("\n🔍 Getting ERIC token information...")
    try:
        subprocess.run([sys.executable, "get_token_info.py"])
        return True
    except Exception as e:
        print(f"❌ Error running token info script: {e}")
        return False

def main():
    print("🚀 ERIC Token Listener Setup")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not os.path.exists("requirements.txt"):
        print("❌ requirements.txt not found. Make sure you're in the correct directory.")
        return
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements. Please install manually:")
        print("pip install -r requirements.txt")
        return
    
    # Get token information
    print("\n" + "=" * 40)
    run_token_info()
    
    print("\n" + "=" * 40)
    print("📋 Next Steps:")
    print("1. Copy the mint address from the output above")
    print("2. Edit token_listener.py and replace 'ERIC_TOKEN_MINT' with the actual address")
    print("3. Run: python token_listener.py")
    print("=" * 40)

if __name__ == "__main__":
    main()
